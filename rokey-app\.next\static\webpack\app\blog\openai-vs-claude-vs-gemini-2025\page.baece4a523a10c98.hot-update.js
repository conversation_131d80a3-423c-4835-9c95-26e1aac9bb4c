"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/blog/openai-vs-claude-vs-gemini-2025/page",{

/***/ "(app-pages-browser)/./src/app/blog/openai-vs-claude-vs-gemini-2025/page.tsx":
/*!***************************************************************!*\
  !*** ./src/app/blog/openai-vs-claude-vs-gemini-2025/page.tsx ***!
  \***************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OpenAIVsClaudeVsGemini)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ClockIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ClockIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ClockIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/landing/LandingNavbar */ \"(app-pages-browser)/./src/components/landing/LandingNavbar.tsx\");\n/* harmony import */ var _components_landing_Footer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/landing/Footer */ \"(app-pages-browser)/./src/components/landing/Footer.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n// Helper function for consistent date formatting\nconst formatDate = (dateString)=>{\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric'\n    });\n};\nfunction OpenAIVsClaudeVsGemini() {\n    const post = {\n        title: 'OpenAI vs Claude vs Gemini vs Grok: Which AI Model for What Task in 2025?',\n        author: 'David Okoro',\n        date: '2025-01-18',\n        readTime: '15 min read',\n        category: 'AI Comparison',\n        tags: [\n            'OpenAI GPT o3',\n            'Claude 4 Opus',\n            'Gemini 2.5 Pro',\n            'Grok 4',\n            'AI Model Comparison',\n            'Best AI 2025',\n            'AI API',\n            'Machine Learning',\n            'AI Benchmarks',\n            'LLM Performance'\n        ],\n        excerpt: 'Complete 2025 comparison of OpenAI GPT o3, Claude 4 Opus, Gemini 2.5 Pro, and Grok 4. Latest performance benchmarks, pricing, coding capabilities, and the best AI model for each specific task.'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"pt-20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-gradient-to-br from-gray-50 to-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-6xl mx-auto px-6 sm:px-8 lg:px-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/blog\",\n                                            className: \"text-[#ff6b35] hover:text-[#e55a2b] font-medium\",\n                                            children: \"← Back to Blog\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                            lineNumber: 44,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                        lineNumber: 43,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-[#ff6b35] text-white px-3 py-1 rounded-full text-sm font-medium\",\n                                            children: post.category\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                            lineNumber: 50,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight\",\n                                        children: post.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-gray-600 mb-8 leading-relaxed\",\n                                        children: post.excerpt\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-6 text-sm text-gray-500 mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                        lineNumber: 65,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    post.author\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                lineNumber: 64,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                        lineNumber: 69,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    formatDate(post.date)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                lineNumber: 68,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                        lineNumber: 73,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    post.readTime\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                lineNumber: 72,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2 mb-8\",\n                                        children: post.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm\",\n                                                children: tag\n                                            }, tag, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                lineNumber: 80,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                lineNumber: 38,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-6xl mx-auto px-6 sm:px-8 lg:px-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.article, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.2\n                                },\n                                className: \"prose prose-lg max-w-none\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"aspect-video rounded-2xl mb-12 relative overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: \"https://images.unsplash.com/photo-1677442136019-21780ecad995?fm=jpg&q=80&w=2000&ixlib=rb-4.1.0\",\n                                                alt: \"AI Model Comparison - Multiple AI robots representing different AI models\",\n                                                className: \"w-full h-full object-cover\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-br from-[#ff6b35]/80 to-[#f7931e]/80 rounded-2xl\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-white text-2xl font-bold text-center px-8\",\n                                                    children: \"OpenAI vs Claude vs Gemini vs Grok: The Ultimate AI Showdown\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-800 space-y-6 text-lg leading-relaxed\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"The AI landscape in 2025 has reached unprecedented heights. With OpenAI's revolutionary GPT o3, Anthropic's Claude 4 Opus, Google's Gemini 2.5 Pro, and xAI's Grok 4 all pushing the boundaries of artificial intelligence, choosing the right AI model for your specific task has become both crucial and complex. This comprehensive comparison will help you make the right choice based on the latest performance benchmarks, cost analysis, and real-world use cases.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-blue-50 border-l-4 border-blue-500 p-6 my-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-blue-900 mb-2\",\n                                                        children: \"\\uD83C\\uDFAF Key Takeaway\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                        lineNumber: 121,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-blue-800\",\n                                                        children: \"No single AI model dominates every task. The best choice depends on your specific needs: coding, writing, reasoning, multimodal tasks, or cost optimization. This guide breaks down exactly which model excels where.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                        lineNumber: 122,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-gray-900 mt-12 mb-6\",\n                                                children: \"The Contenders: 2025's Top AI Models\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6 my-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-green-50 border border-green-200 rounded-lg p-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-xl font-semibold text-green-900 mb-3\",\n                                                                children: \"\\uD83D\\uDE80 OpenAI GPT o3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 131,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: \"text-green-800 space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• 200K context window\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 133,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• $2.00 input / $8.00 output per 1M tokens\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 134,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Revolutionary reasoning capabilities\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 135,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Advanced multimodal processing\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 136,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Optimized for complex problem-solving\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 137,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 132,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-purple-50 border border-purple-200 rounded-lg p-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-xl font-semibold text-purple-900 mb-3\",\n                                                                children: \"\\uD83E\\uDDE0 Claude 4 Opus\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 142,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: \"text-purple-800 space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• 500K context window\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 144,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• $15.00 input / $75.00 output per 1M tokens\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 145,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Superior coding and reasoning\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 146,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Advanced safety alignment\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 147,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Best for complex creative tasks\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 148,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 143,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                        lineNumber: 141,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-blue-50 border border-blue-200 rounded-lg p-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-xl font-semibold text-blue-900 mb-3\",\n                                                                children: \"\\uD83C\\uDF1F Gemini 2.5 Pro\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 153,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: \"text-blue-800 space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• 2M context window (largest)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 155,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• $1.25-$2.50 input / $10.00-$15.00 output per 1M tokens\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 156,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Advanced multimodal capabilities\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 157,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Google Search integration\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 158,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Best for scientific reasoning\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 159,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 154,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                        lineNumber: 152,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-orange-50 border border-orange-200 rounded-lg p-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-xl font-semibold text-orange-900 mb-3\",\n                                                                children: \"⚡ Grok 4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 164,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: \"text-orange-800 space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• 256K context window\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 166,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• $3.00 input / $15.00 output per 1M tokens\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 167,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Real-time X (Twitter) data access\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 168,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Uncensored and conversational\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 169,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Function calling & structured outputs\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 170,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 165,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-gray-900 mt-12 mb-6\",\n                                                children: \"Performance Benchmarks: The Numbers Don't Lie\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"overflow-x-auto my-8\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                    className: \"w-full border-collapse border border-gray-300\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                className: \"bg-gray-100\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"border border-gray-300 px-4 py-3 text-left font-semibold\",\n                                                                        children: \"Benchmark\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 181,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"border border-gray-300 px-4 py-3 text-center font-semibold\",\n                                                                        children: \"GPT o3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 182,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"border border-gray-300 px-4 py-3 text-center font-semibold\",\n                                                                        children: \"Claude 4 Opus\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 183,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"border border-gray-300 px-4 py-3 text-center font-semibold\",\n                                                                        children: \"Gemini 2.5 Pro\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 184,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"border border-gray-300 px-4 py-3 text-center font-semibold\",\n                                                                        children: \"Grok 4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 185,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 180,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                            lineNumber: 179,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 px-4 py-3 font-medium\",\n                                                                            children: \"MMLU Pro (Advanced Knowledge)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 190,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 px-4 py-3 text-center\",\n                                                                            children: \"84.2%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 191,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 px-4 py-3 text-center\",\n                                                                            children: \"86.1%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 192,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 px-4 py-3 text-center bg-green-50\",\n                                                                            children: \"87.3%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 193,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 px-4 py-3 text-center\",\n                                                                            children: \"85.3%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 194,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                    lineNumber: 189,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 px-4 py-3 font-medium\",\n                                                                            children: \"HumanEval (Coding)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 197,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 px-4 py-3 text-center\",\n                                                                            children: \"89.7%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 198,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 px-4 py-3 text-center bg-green-50\",\n                                                                            children: \"92.3%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 199,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 px-4 py-3 text-center\",\n                                                                            children: \"87.4%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 200,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 px-4 py-3 text-center\",\n                                                                            children: \"88.1%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 201,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                    lineNumber: 196,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 px-4 py-3 font-medium\",\n                                                                            children: \"GPQA Diamond (Scientific Reasoning)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 204,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 px-4 py-3 text-center\",\n                                                                            children: \"85.5%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 205,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 px-4 py-3 text-center\",\n                                                                            children: \"84.9%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 206,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 px-4 py-3 text-center bg-green-50\",\n                                                                            children: \"86.4%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 207,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 px-4 py-3 text-center\",\n                                                                            children: \"83.7%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 208,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                    lineNumber: 203,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 px-4 py-3 font-medium\",\n                                                                            children: \"SWE-bench Verified (Real-world Coding)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 211,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 px-4 py-3 text-center\",\n                                                                            children: \"87.2%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 212,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 px-4 py-3 text-center bg-green-50\",\n                                                                            children: \"89.7%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 213,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 px-4 py-3 text-center\",\n                                                                            children: \"85.1%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 214,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 px-4 py-3 text-center\",\n                                                                            children: \"84.3%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 215,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                    lineNumber: 210,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 px-4 py-3 font-medium\",\n                                                                            children: \"MMMU (Multimodal Understanding)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 218,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 px-4 py-3 text-center\",\n                                                                            children: \"74.8%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 219,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 px-4 py-3 text-center\",\n                                                                            children: \"75.2%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 220,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 px-4 py-3 text-center bg-green-50\",\n                                                                            children: \"78.9%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 221,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 px-4 py-3 text-center\",\n                                                                            children: \"76.5%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 222,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                    lineNumber: 217,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                            lineNumber: 188,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-gray-900 mt-12 mb-6\",\n                                                children: \"Task-Specific Recommendations: Which AI Model to Choose\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-2xl font-semibold text-blue-900 mb-4\",\n                                                                children: \"\\uD83D\\uDCBB Software Development & Coding\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 232,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-blue-800 mb-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Winner: Claude 4 Opus\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                    lineNumber: 233,\n                                                                    columnNumber: 55\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 233,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-blue-700\",\n                                                                children: \"Claude 4 Opus dominates coding benchmarks with 92.3% on HumanEval and 89.7% on SWE-bench Verified. It excels at complex code generation, architectural design, debugging, and explaining intricate algorithms. Best for: Python, JavaScript, Rust, React, API development, and comprehensive code reviews.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 234,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-4 p-4 bg-blue-100 rounded\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-blue-800\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                            children: \"Pro Tip:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 238,\n                                                                            columnNumber: 60\n                                                                        }, this),\n                                                                        \" Use Claude 4 Opus for complex coding tasks and architectural decisions, then GPT o3 for rapid prototyping and iteration.\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                    lineNumber: 238,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 237,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-200 rounded-lg p-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-2xl font-semibold text-purple-900 mb-4\",\n                                                                children: \"✍️ Content Writing & Creative Tasks\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 243,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-purple-800 mb-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Winner: Claude 4 Opus\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                    lineNumber: 244,\n                                                                    columnNumber: 57\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 244,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-purple-700\",\n                                                                children: \"Claude 4 Opus produces the most nuanced, well-structured content with superior reasoning and creativity. Its 500K context window allows for maintaining consistency across very long documents and complex narratives. Best for: Blog posts, technical documentation, creative writing, storytelling, and in-depth analysis.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 245,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-4 p-4 bg-purple-100 rounded\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-purple-800\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                            children: \"Pro Tip:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 249,\n                                                                            columnNumber: 62\n                                                                        }, this),\n                                                                        \" Claude 4 Opus excels at maintaining brand voice and tone across multiple pieces of content while handling complex creative requirements.\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                    lineNumber: 249,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 248,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg p-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-2xl font-semibold text-green-900 mb-4\",\n                                                                children: \"\\uD83D\\uDCCA Data Analysis & Research\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 254,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-green-800 mb-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Winner: Gemini 2.5 Pro\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                    lineNumber: 255,\n                                                                    columnNumber: 56\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 255,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-green-700\",\n                                                                children: \"With its massive 2M context window, Google Search integration, and 86.4% GPQA Diamond score, Gemini 2.5 Pro excels at processing enormous datasets and providing real-time insights. Advanced multimodal capabilities handle charts, graphs, and visual data seamlessly. Best for: Market research, data visualization, trend analysis, scientific research, and complex mathematical reasoning.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 256,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-4 p-4 bg-green-100 rounded\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-green-800\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                            children: \"Pro Tip:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 260,\n                                                                            columnNumber: 61\n                                                                        }, this),\n                                                                        \" Combine Gemini 2.5 Pro's analytical power with Grok 4's real-time data access for comprehensive market intelligence.\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                    lineNumber: 260,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 259,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                        lineNumber: 253,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-r from-orange-50 to-red-50 border border-orange-200 rounded-lg p-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-2xl font-semibold text-orange-900 mb-4\",\n                                                                children: \"\\uD83D\\uDDE3️ Conversational AI & Social Media\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 265,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-orange-800 mb-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Winner: Grok AI\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                    lineNumber: 266,\n                                                                    columnNumber: 57\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 266,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-orange-700\",\n                                                                children: \"Grok's real-time access to X (Twitter) data and conversational personality make it ideal for social media management and trend analysis. Best for: Social media content, trend monitoring, conversational chatbots, and real-time insights.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 267,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-4 p-4 bg-orange-100 rounded\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-orange-800\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                            children: \"Pro Tip:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 271,\n                                                                            columnNumber: 62\n                                                                        }, this),\n                                                                        \" Grok's less restrictive nature makes it valuable for creative brainstorming and unconventional problem-solving.\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                    lineNumber: 271,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 270,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-gray-900 mt-12 mb-6\",\n                                                children: \"Cost Analysis: Getting the Best Value\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-6 my-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-yellow-900 mb-4\",\n                                                        children: \"\\uD83D\\uDCB0 Pricing Breakdown (per 1M tokens)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold text-yellow-800 mb-2\",\n                                                                        children: \"Input Tokens:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 282,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                        className: \"text-yellow-700 space-y-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"• Gemini 2.5 Pro: $1.25-$2.50 (cheapest)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                                lineNumber: 284,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"• GPT o3: $2.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                                lineNumber: 285,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"• Grok 4: $3.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                                lineNumber: 286,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"• Claude 4 Opus: $15.00 (most expensive)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                                lineNumber: 287,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 283,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 281,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold text-yellow-800 mb-2\",\n                                                                        children: \"Output Tokens:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 291,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                        className: \"text-yellow-700 space-y-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"• GPT o3: $8.00 (cheapest)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                                lineNumber: 293,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"• Gemini 2.5 Pro: $10.00-$15.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                                lineNumber: 294,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"• Grok 4: $15.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                                lineNumber: 295,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"• Claude 4 Opus: $75.00 (most expensive)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                                lineNumber: 296,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 292,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 290,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-4 p-4 bg-yellow-100 rounded\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-yellow-800\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Note:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                    lineNumber: 301,\n                                                                    columnNumber: 60\n                                                                }, this),\n                                                                \" Gemini 2.5 Pro pricing varies by prompt size (≤200k vs >200k tokens). All prices are per 1 million tokens.\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-gray-900 mt-12 mb-6\",\n                                                children: \"The Smart Solution: Multi-Model AI Routing\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Instead of choosing just one AI model, the smartest approach in 2025 is to use the right model for each specific task. This is where AI routing platforms like RouKey become invaluable.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-r from-[#ff6b35]/10 to-[#f7931e]/10 border border-[#ff6b35]/30 rounded-lg p-8 my-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-2xl font-semibold text-gray-900 mb-4\",\n                                                        children: \"\\uD83D\\uDE80 Why Multi-Model Routing is the Future\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                        lineNumber: 312,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"space-y-3 text-gray-700\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                className: \"flex items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-[#ff6b35] mr-3 mt-1\",\n                                                                        children: \"•\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 315,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                children: \"Cost Optimization:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                                lineNumber: 316,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            \" Use cheaper models for simple tasks, premium models for complex ones\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 316,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 314,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                className: \"flex items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-[#ff6b35] mr-3 mt-1\",\n                                                                        children: \"•\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 319,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                children: \"Performance Maximization:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                                lineNumber: 320,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            \" Route each task to the model that performs best for that specific use case\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 320,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 318,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                className: \"flex items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-[#ff6b35] mr-3 mt-1\",\n                                                                        children: \"•\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 323,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                children: \"Reliability:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                                lineNumber: 324,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            \" Automatic fallbacks ensure your application never goes down\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 324,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 322,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                className: \"flex items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-[#ff6b35] mr-3 mt-1\",\n                                                                        children: \"•\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 327,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                children: \"Future-Proof:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                                lineNumber: 328,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            \" Easily add new models as they become available\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 328,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 326,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                        lineNumber: 313,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-gray-900 mt-12 mb-6\",\n                                                children: \"Conclusion: The Multi-Model Future\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                lineNumber: 333,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"The AI model wars of 2025 have produced revolutionary breakthroughs, but the real winner is the user who can leverage all these models strategically. Claude 4 Opus for complex coding and reasoning, GPT o3 for rapid iteration and creative tasks, Gemini 2.5 Pro for data analysis and research, and Grok 4 for real-time insights and social intelligence – each has its place in a well-architected AI system.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"The future belongs to intelligent routing systems that can automatically select the best model for each task, optimize costs, and provide seamless fallbacks. This isn't just about having access to multiple models – it's about using them intelligently to maximize performance while minimizing costs.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-r from-[#ff6b35] to-[#f7931e] rounded-2xl p-8 text-white my-12\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-2xl font-bold mb-4\",\n                                                        children: \"Ready to Use All AI Models Intelligently?\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg mb-6 opacity-90\",\n                                                        children: \"Stop choosing between AI models. RouKey's intelligent routing lets you use GPT o3, Claude 4 Opus, Gemini 2.5 Pro, and Grok 4 seamlessly with your own API keys. Save up to 70% on AI costs while maximizing performance with the latest 2025 models.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col sm:flex-row gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                                href: \"/\",\n                                                                className: \"inline-flex items-center justify-center px-6 py-3 bg-white text-[#ff6b35] font-semibold rounded-lg hover:bg-gray-100 transition-colors\",\n                                                                children: \"Start Free with RouKey\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 350,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                                href: \"/pricing\",\n                                                                className: \"inline-flex items-center justify-center px-6 py-3 border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-[#ff6b35] transition-colors\",\n                                                                children: \"View Pricing\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 356,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_Footer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                lineNumber: 370,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n_c = OpenAIVsClaudeVsGemini;\nvar _c;\n$RefreshReg$(_c, \"OpenAIVsClaudeVsGemini\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/blog/openai-vs-claude-vs-gemini-2025/page.tsx\n"));

/***/ })

});