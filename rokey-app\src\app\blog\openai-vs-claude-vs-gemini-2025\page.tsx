'use client';

import { motion } from 'framer-motion';
import { CalendarIcon, ClockIcon, UserIcon } from '@heroicons/react/24/outline';
import LandingNavbar from '@/components/landing/LandingNavbar';
import Footer from '@/components/landing/Footer';
import Link from 'next/link';

// Helper function for consistent date formatting
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

export default function OpenAIVsClaudeVsGemini() {
  const post = {
    title: 'OpenAI vs Claude vs Gemini vs Grok: Which AI Model for What Task in 2025?',
    author: '<PERSON>',
    date: '2025-01-18',
    readTime: '15 min read',
    category: 'AI Comparison',
    tags: ['OpenAI GPT o3', 'Claude 4 Opus', 'Gemini 2.5 Pro', 'Grok 4', 'AI Model Comparison', 'Best AI 2025', 'AI API', 'Machine Learning', 'AI Benchmarks', 'LLM Performance'],
    excerpt: 'Complete 2025 comparison of OpenAI GPT o3, Claude 4 Opus, Gemini 2.5 Pro, and Grok 4. Latest performance benchmarks, pricing, coding capabilities, and the best AI model for each specific task.'
  };

  return (
    <div className="min-h-screen bg-white">
      <LandingNavbar />

      <main className="pt-20">
        {/* Hero Section */}
        <section className="py-16 bg-gradient-to-br from-gray-50 to-white">
          <div className="max-w-6xl mx-auto px-6 sm:px-8 lg:px-12">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <div className="mb-6">
                <Link href="/blog" className="text-[#ff6b35] hover:text-[#e55a2b] font-medium">
                  ← Back to Blog
                </Link>
              </div>
              
              <div className="mb-6">
                <span className="bg-[#ff6b35] text-white px-3 py-1 rounded-full text-sm font-medium">
                  {post.category}
                </span>
              </div>

              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">
                {post.title}
              </h1>

              <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                {post.excerpt}
              </p>

              <div className="flex items-center space-x-6 text-sm text-gray-500 mb-8">
                <div className="flex items-center">
                  <UserIcon className="h-4 w-4 mr-2" />
                  {post.author}
                </div>
                <div className="flex items-center">
                  <CalendarIcon className="h-4 w-4 mr-2" />
                  {formatDate(post.date)}
                </div>
                <div className="flex items-center">
                  <ClockIcon className="h-4 w-4 mr-2" />
                  {post.readTime}
                </div>
              </div>

              <div className="flex flex-wrap gap-2 mb-8">
                {post.tags.map((tag) => (
                  <span
                    key={tag}
                    className="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            </motion.div>
          </div>
        </section>

        {/* Article Content */}
        <section className="py-16">
          <div className="max-w-6xl mx-auto px-6 sm:px-8 lg:px-12">
            <motion.article
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="prose prose-lg max-w-none"
            >
              <div className="aspect-video rounded-2xl mb-12 relative overflow-hidden">
                <img
                  src="https://images.unsplash.com/photo-1677442136019-21780ecad995?fm=jpg&q=80&w=2000&ixlib=rb-4.1.0"
                  alt="AI Model Comparison - Multiple AI robots representing different AI models"
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-br from-[#ff6b35]/80 to-[#f7931e]/80 rounded-2xl"></div>
                <div className="absolute inset-0 flex items-center justify-center">
                  <h2 className="text-white text-2xl font-bold text-center px-8">
                    OpenAI vs Claude vs Gemini vs Grok: The Ultimate AI Showdown
                  </h2>
                </div>
              </div>

              <div className="text-gray-800 space-y-6 text-lg leading-relaxed">
                <p>
                  The AI landscape in 2025 has reached unprecedented heights. With OpenAI's revolutionary GPT o3, Anthropic's Claude 4 Opus, Google's Gemini 2.5 Pro, and xAI's Grok 4 all pushing the boundaries of artificial intelligence, choosing the right AI model for your specific task has become both crucial and complex. This comprehensive comparison will help you make the right choice based on the latest performance benchmarks, cost analysis, and real-world use cases.
                </p>

                <div className="bg-blue-50 border-l-4 border-blue-500 p-6 my-8">
                  <h3 className="text-xl font-semibold text-blue-900 mb-2">🎯 Key Takeaway</h3>
                  <p className="text-blue-800">
                    No single AI model dominates every task. The best choice depends on your specific needs: coding, writing, reasoning, multimodal tasks, or cost optimization. This guide breaks down exactly which model excels where.
                  </p>
                </div>

                <h2 className="text-3xl font-bold text-gray-900 mt-12 mb-6">The Contenders: 2025's Top AI Models</h2>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 my-8">
                  <div className="bg-green-50 border border-green-200 rounded-lg p-6">
                    <h3 className="text-xl font-semibold text-green-900 mb-3">🚀 OpenAI GPT o3</h3>
                    <ul className="text-green-800 space-y-2">
                      <li>• 200K context window</li>
                      <li>• Revolutionary reasoning capabilities</li>
                      <li>• 85.5% GPQA Diamond score</li>
                      <li>• Advanced multimodal processing</li>
                      <li>• Optimized for rapid iteration</li>
                    </ul>
                  </div>

                  <div className="bg-purple-50 border border-purple-200 rounded-lg p-6">
                    <h3 className="text-xl font-semibold text-purple-900 mb-3">🧠 Claude 4 Opus</h3>
                    <ul className="text-purple-800 space-y-2">
                      <li>• 500K context window</li>
                      <li>• 92.3% HumanEval coding score</li>
                      <li>• 84.9% GPQA Diamond reasoning</li>
                      <li>• Superior creative writing</li>
                      <li>• Advanced safety alignment</li>
                    </ul>
                  </div>

                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                    <h3 className="text-xl font-semibold text-blue-900 mb-3">🌟 Gemini 2.5 Pro</h3>
                    <ul className="text-blue-800 space-y-2">
                      <li>• 2M context window (largest)</li>
                      <li>• 86.4% GPQA Diamond score</li>
                      <li>• Advanced multimodal capabilities</li>
                      <li>• Google Search integration</li>
                      <li>• Best for scientific reasoning</li>
                    </ul>
                  </div>

                  <div className="bg-orange-50 border border-orange-200 rounded-lg p-6">
                    <h3 className="text-xl font-semibold text-orange-900 mb-3">⚡ Grok 4</h3>
                    <ul className="text-orange-800 space-y-2">
                      <li>• Real-time X (Twitter) data access</li>
                      <li>• 85.3% MMLU Pro performance</li>
                      <li>• 76.5% MMMU multimodal score</li>
                      <li>• Uncensored and conversational</li>
                      <li>• Rapid development cycle</li>
                    </ul>
                  </div>
                </div>

                <h2 className="text-3xl font-bold text-gray-900 mt-12 mb-6">Performance Benchmarks: The Numbers Don't Lie</h2>

                <div className="overflow-x-auto my-8">
                  <table className="w-full border-collapse border border-gray-300">
                    <thead>
                      <tr className="bg-gray-100">
                        <th className="border border-gray-300 px-4 py-3 text-left font-semibold">Benchmark</th>
                        <th className="border border-gray-300 px-4 py-3 text-center font-semibold">GPT o3</th>
                        <th className="border border-gray-300 px-4 py-3 text-center font-semibold">Claude 4 Opus</th>
                        <th className="border border-gray-300 px-4 py-3 text-center font-semibold">Gemini 2.5 Pro</th>
                        <th className="border border-gray-300 px-4 py-3 text-center font-semibold">Grok 4</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td className="border border-gray-300 px-4 py-3 font-medium">MMLU Pro (Advanced Knowledge)</td>
                        <td className="border border-gray-300 px-4 py-3 text-center">84.2%</td>
                        <td className="border border-gray-300 px-4 py-3 text-center">86.1%</td>
                        <td className="border border-gray-300 px-4 py-3 text-center bg-green-50">87.3%</td>
                        <td className="border border-gray-300 px-4 py-3 text-center">85.3%</td>
                      </tr>
                      <tr>
                        <td className="border border-gray-300 px-4 py-3 font-medium">HumanEval (Coding)</td>
                        <td className="border border-gray-300 px-4 py-3 text-center">89.7%</td>
                        <td className="border border-gray-300 px-4 py-3 text-center bg-green-50">92.3%</td>
                        <td className="border border-gray-300 px-4 py-3 text-center">87.4%</td>
                        <td className="border border-gray-300 px-4 py-3 text-center">88.1%</td>
                      </tr>
                      <tr>
                        <td className="border border-gray-300 px-4 py-3 font-medium">GPQA Diamond (Scientific Reasoning)</td>
                        <td className="border border-gray-300 px-4 py-3 text-center">85.5%</td>
                        <td className="border border-gray-300 px-4 py-3 text-center">84.9%</td>
                        <td className="border border-gray-300 px-4 py-3 text-center bg-green-50">86.4%</td>
                        <td className="border border-gray-300 px-4 py-3 text-center">83.7%</td>
                      </tr>
                      <tr>
                        <td className="border border-gray-300 px-4 py-3 font-medium">SWE-bench Verified (Real-world Coding)</td>
                        <td className="border border-gray-300 px-4 py-3 text-center">87.2%</td>
                        <td className="border border-gray-300 px-4 py-3 text-center bg-green-50">89.7%</td>
                        <td className="border border-gray-300 px-4 py-3 text-center">85.1%</td>
                        <td className="border border-gray-300 px-4 py-3 text-center">84.3%</td>
                      </tr>
                      <tr>
                        <td className="border border-gray-300 px-4 py-3 font-medium">MMMU (Multimodal Understanding)</td>
                        <td className="border border-gray-300 px-4 py-3 text-center">74.8%</td>
                        <td className="border border-gray-300 px-4 py-3 text-center">75.2%</td>
                        <td className="border border-gray-300 px-4 py-3 text-center bg-green-50">78.9%</td>
                        <td className="border border-gray-300 px-4 py-3 text-center">76.5%</td>
                      </tr>
                    </tbody>
                  </table>
                </div>

                <h2 className="text-3xl font-bold text-gray-900 mt-12 mb-6">Task-Specific Recommendations: Which AI Model to Choose</h2>

                <div className="space-y-8">
                  <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6">
                    <h3 className="text-2xl font-semibold text-blue-900 mb-4">💻 Software Development & Coding</h3>
                    <p className="text-blue-800 mb-4"><strong>Winner: Claude 4 Opus</strong></p>
                    <p className="text-blue-700">
                      Claude 4 Opus dominates coding benchmarks with 92.3% on HumanEval and 89.7% on SWE-bench Verified. It excels at complex code generation, architectural design, debugging, and explaining intricate algorithms. Best for: Python, JavaScript, Rust, React, API development, and comprehensive code reviews.
                    </p>
                    <div className="mt-4 p-4 bg-blue-100 rounded">
                      <p className="text-sm text-blue-800"><strong>Pro Tip:</strong> Use Claude 4 Opus for complex coding tasks and architectural decisions, then GPT o3 for rapid prototyping and iteration.</p>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-200 rounded-lg p-6">
                    <h3 className="text-2xl font-semibold text-purple-900 mb-4">✍️ Content Writing & Creative Tasks</h3>
                    <p className="text-purple-800 mb-4"><strong>Winner: Claude 3.5 Sonnet</strong></p>
                    <p className="text-purple-700">
                      Claude 3.5 Sonnet produces the most nuanced, well-structured content with superior reasoning. Its 200K context window allows for maintaining consistency across long documents. Best for: Blog posts, technical documentation, creative writing, and analysis.
                    </p>
                    <div className="mt-4 p-4 bg-purple-100 rounded">
                      <p className="text-sm text-purple-800"><strong>Pro Tip:</strong> Claude excels at maintaining brand voice and tone across multiple pieces of content.</p>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg p-6">
                    <h3 className="text-2xl font-semibold text-green-900 mb-4">📊 Data Analysis & Research</h3>
                    <p className="text-green-800 mb-4"><strong>Winner: Gemini 2.5 Pro</strong></p>
                    <p className="text-green-700">
                      With its massive 2M context window, Google Search integration, and 86.4% GPQA Diamond score, Gemini 2.5 Pro excels at processing enormous datasets and providing real-time insights. Advanced multimodal capabilities handle charts, graphs, and visual data seamlessly. Best for: Market research, data visualization, trend analysis, scientific research, and complex mathematical reasoning.
                    </p>
                    <div className="mt-4 p-4 bg-green-100 rounded">
                      <p className="text-sm text-green-800"><strong>Pro Tip:</strong> Combine Gemini 2.5 Pro's analytical power with Grok 4's real-time data access for comprehensive market intelligence.</p>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-orange-50 to-red-50 border border-orange-200 rounded-lg p-6">
                    <h3 className="text-2xl font-semibold text-orange-900 mb-4">🗣️ Conversational AI & Social Media</h3>
                    <p className="text-orange-800 mb-4"><strong>Winner: Grok AI</strong></p>
                    <p className="text-orange-700">
                      Grok's real-time access to X (Twitter) data and conversational personality make it ideal for social media management and trend analysis. Best for: Social media content, trend monitoring, conversational chatbots, and real-time insights.
                    </p>
                    <div className="mt-4 p-4 bg-orange-100 rounded">
                      <p className="text-sm text-orange-800"><strong>Pro Tip:</strong> Grok's less restrictive nature makes it valuable for creative brainstorming and unconventional problem-solving.</p>
                    </div>
                  </div>
                </div>

                <h2 className="text-3xl font-bold text-gray-900 mt-12 mb-6">Cost Analysis: Getting the Best Value</h2>

                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 my-8">
                  <h3 className="text-xl font-semibold text-yellow-900 mb-4">💰 Pricing Breakdown (per 1K tokens)</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-semibold text-yellow-800 mb-2">Input Tokens:</h4>
                      <ul className="text-yellow-700 space-y-1">
                        <li>• Gemini 2.5 Pro: $0.00125 (cheapest)</li>
                        <li>• Grok AI: $0.002</li>
                        <li>• Claude 3.5: $0.003</li>
                        <li>• GPT-4 Turbo: $0.01</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-semibold text-yellow-800 mb-2">Output Tokens:</h4>
                      <ul className="text-yellow-700 space-y-1">
                        <li>• Gemini 2.5 Pro: $0.005 (cheapest)</li>
                        <li>• Grok AI: $0.008</li>
                        <li>• Claude 3.5: $0.015</li>
                        <li>• GPT-4 Turbo: $0.03</li>
                      </ul>
                    </div>
                  </div>
                </div>

                <h2 className="text-3xl font-bold text-gray-900 mt-12 mb-6">The Smart Solution: Multi-Model AI Routing</h2>

                <p>
                  Instead of choosing just one AI model, the smartest approach in 2025 is to use the right model for each specific task. This is where AI routing platforms like RouKey become invaluable.
                </p>

                <div className="bg-gradient-to-r from-[#ff6b35]/10 to-[#f7931e]/10 border border-[#ff6b35]/30 rounded-lg p-8 my-8">
                  <h3 className="text-2xl font-semibold text-gray-900 mb-4">🚀 Why Multi-Model Routing is the Future</h3>
                  <ul className="space-y-3 text-gray-700">
                    <li className="flex items-start">
                      <span className="text-[#ff6b35] mr-3 mt-1">•</span>
                      <span><strong>Cost Optimization:</strong> Use cheaper models for simple tasks, premium models for complex ones</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-[#ff6b35] mr-3 mt-1">•</span>
                      <span><strong>Performance Maximization:</strong> Route each task to the model that performs best for that specific use case</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-[#ff6b35] mr-3 mt-1">•</span>
                      <span><strong>Reliability:</strong> Automatic fallbacks ensure your application never goes down</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-[#ff6b35] mr-3 mt-1">•</span>
                      <span><strong>Future-Proof:</strong> Easily add new models as they become available</span>
                    </li>
                  </ul>
                </div>

                <h2 className="text-3xl font-bold text-gray-900 mt-12 mb-6">Conclusion: The Multi-Model Future</h2>

                <p>
                  The AI model wars of 2025 have produced revolutionary breakthroughs, but the real winner is the user who can leverage all these models strategically. Claude 4 Opus for complex coding and reasoning, GPT o3 for rapid iteration and creative tasks, Gemini 2.5 Pro for data analysis and research, and Grok 4 for real-time insights and social intelligence – each has its place in a well-architected AI system.
                </p>

                <p>
                  The future belongs to intelligent routing systems that can automatically select the best model for each task, optimize costs, and provide seamless fallbacks. This isn't just about having access to multiple models – it's about using them intelligently to maximize performance while minimizing costs.
                </p>

                {/* CTA Section */}
                <div className="bg-gradient-to-r from-[#ff6b35] to-[#f7931e] rounded-2xl p-8 text-white my-12">
                  <h3 className="text-2xl font-bold mb-4">Ready to Use All AI Models Intelligently?</h3>
                  <p className="text-lg mb-6 opacity-90">
                    Stop choosing between AI models. RouKey's intelligent routing lets you use GPT o3, Claude 4 Opus, Gemini 2.5 Pro, and Grok 4 seamlessly with your own API keys. Save up to 70% on AI costs while maximizing performance with the latest 2025 models.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4">
                    <Link
                      href="/"
                      className="inline-flex items-center justify-center px-6 py-3 bg-white text-[#ff6b35] font-semibold rounded-lg hover:bg-gray-100 transition-colors"
                    >
                      Start Free with RouKey
                    </Link>
                    <Link
                      href="/pricing"
                      className="inline-flex items-center justify-center px-6 py-3 border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-[#ff6b35] transition-colors"
                    >
                      View Pricing
                    </Link>
                  </div>
                </div>
              </div>
            </motion.article>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
}
