"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/blog/openai-vs-claude-vs-gemini-2025/page",{

/***/ "(app-pages-browser)/./src/app/blog/openai-vs-claude-vs-gemini-2025/page.tsx":
/*!***************************************************************!*\
  !*** ./src/app/blog/openai-vs-claude-vs-gemini-2025/page.tsx ***!
  \***************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OpenAIVsClaudeVsGemini)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ClockIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ClockIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CalendarIcon,ClockIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/landing/LandingNavbar */ \"(app-pages-browser)/./src/components/landing/LandingNavbar.tsx\");\n/* harmony import */ var _components_landing_Footer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/landing/Footer */ \"(app-pages-browser)/./src/components/landing/Footer.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n// Helper function for consistent date formatting\nconst formatDate = (dateString)=>{\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric'\n    });\n};\nfunction OpenAIVsClaudeVsGemini() {\n    const post = {\n        title: 'OpenAI vs Claude vs Gemini vs Grok: Which AI Model for What Task in 2025?',\n        author: 'David Okoro',\n        date: '2025-01-18',\n        readTime: '15 min read',\n        category: 'AI Comparison',\n        tags: [\n            'OpenAI GPT o3',\n            'Claude 4 Opus',\n            'Gemini 2.5 Pro',\n            'Grok 4',\n            'AI Model Comparison',\n            'Best AI 2025',\n            'AI API',\n            'Machine Learning',\n            'AI Benchmarks',\n            'LLM Performance'\n        ],\n        excerpt: 'Complete 2025 comparison of OpenAI GPT o3, Claude 4 Opus, Gemini 2.5 Pro, and Grok 4. Latest performance benchmarks, pricing, coding capabilities, and the best AI model for each specific task.'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"pt-20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-gradient-to-br from-gray-50 to-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-6xl mx-auto px-6 sm:px-8 lg:px-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/blog\",\n                                            className: \"text-[#ff6b35] hover:text-[#e55a2b] font-medium\",\n                                            children: \"← Back to Blog\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                            lineNumber: 44,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                        lineNumber: 43,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-[#ff6b35] text-white px-3 py-1 rounded-full text-sm font-medium\",\n                                            children: post.category\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                            lineNumber: 50,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight\",\n                                        children: post.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-gray-600 mb-8 leading-relaxed\",\n                                        children: post.excerpt\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-6 text-sm text-gray-500 mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                        lineNumber: 65,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    post.author\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                lineNumber: 64,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                        lineNumber: 69,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    formatDate(post.date)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                lineNumber: 68,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CalendarIcon_ClockIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                        lineNumber: 73,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    post.readTime\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                lineNumber: 72,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2 mb-8\",\n                                        children: post.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm\",\n                                                children: tag\n                                            }, tag, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                lineNumber: 80,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                lineNumber: 38,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-6xl mx-auto px-6 sm:px-8 lg:px-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.article, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.2\n                                },\n                                className: \"prose prose-lg max-w-none\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"aspect-video rounded-2xl mb-12 relative overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: \"https://images.unsplash.com/photo-1677442136019-21780ecad995?fm=jpg&q=80&w=2000&ixlib=rb-4.1.0\",\n                                                alt: \"AI Model Comparison - Multiple AI robots representing different AI models\",\n                                                className: \"w-full h-full object-cover\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-br from-[#ff6b35]/80 to-[#f7931e]/80 rounded-2xl\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-white text-2xl font-bold text-center px-8\",\n                                                    children: \"OpenAI vs Claude vs Gemini vs Grok: The Ultimate AI Showdown\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-800 space-y-6 text-lg leading-relaxed\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"The AI landscape in 2025 has reached unprecedented heights. With OpenAI's revolutionary GPT o3, Anthropic's Claude 4 Opus, Google's Gemini 2.5 Pro, and xAI's Grok 4 all pushing the boundaries of artificial intelligence, choosing the right AI model for your specific task has become both crucial and complex. This comprehensive comparison will help you make the right choice based on the latest performance benchmarks, cost analysis, and real-world use cases.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-blue-50 border-l-4 border-blue-500 p-6 my-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-blue-900 mb-2\",\n                                                        children: \"\\uD83C\\uDFAF Key Takeaway\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                        lineNumber: 121,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-blue-800\",\n                                                        children: \"No single AI model dominates every task. The best choice depends on your specific needs: coding, writing, reasoning, multimodal tasks, or cost optimization. This guide breaks down exactly which model excels where.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                        lineNumber: 122,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-gray-900 mt-12 mb-6\",\n                                                children: \"The Contenders: 2025's Top AI Models\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6 my-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-green-50 border border-green-200 rounded-lg p-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-xl font-semibold text-green-900 mb-3\",\n                                                                children: \"\\uD83D\\uDE80 OpenAI GPT o3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 131,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: \"text-green-800 space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• 200K context window\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 133,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• $2.00 input / $8.00 output per 1M tokens\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 134,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Revolutionary reasoning capabilities\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 135,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Advanced multimodal processing\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 136,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Optimized for complex problem-solving\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 137,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 132,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-purple-50 border border-purple-200 rounded-lg p-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-xl font-semibold text-purple-900 mb-3\",\n                                                                children: \"\\uD83E\\uDDE0 Claude 4 Opus\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 142,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: \"text-purple-800 space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• 500K context window\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 144,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• $15.00 input / $75.00 output per 1M tokens\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 145,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Superior coding and reasoning\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 146,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Advanced safety alignment\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 147,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Best for complex creative tasks\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 148,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 143,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                        lineNumber: 141,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-blue-50 border border-blue-200 rounded-lg p-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-xl font-semibold text-blue-900 mb-3\",\n                                                                children: \"\\uD83C\\uDF1F Gemini 2.5 Pro\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 153,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: \"text-blue-800 space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• 2M context window (largest)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 155,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• $1.25-$2.50 input / $10.00-$15.00 output per 1M tokens\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 156,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Advanced multimodal capabilities\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 157,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Google Search integration\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 158,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Best for scientific reasoning\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 159,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 154,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                        lineNumber: 152,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-orange-50 border border-orange-200 rounded-lg p-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-xl font-semibold text-orange-900 mb-3\",\n                                                                children: \"⚡ Grok 4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 164,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: \"text-orange-800 space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• 256K context window\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 166,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• $3.00 input / $15.00 output per 1M tokens\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 167,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Real-time X (Twitter) data access\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 168,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Uncensored and conversational\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 169,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Function calling & structured outputs\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 170,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 165,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-gray-900 mt-12 mb-6\",\n                                                children: \"Performance Benchmarks: The Numbers Don't Lie\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"overflow-x-auto my-8\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                    className: \"w-full border-collapse border border-gray-300\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                className: \"bg-gray-100\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"border border-gray-300 px-4 py-3 text-left font-semibold\",\n                                                                        children: \"Benchmark\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 181,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"border border-gray-300 px-4 py-3 text-center font-semibold\",\n                                                                        children: \"GPT o3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 182,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"border border-gray-300 px-4 py-3 text-center font-semibold\",\n                                                                        children: \"Claude 4 Opus\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 183,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"border border-gray-300 px-4 py-3 text-center font-semibold\",\n                                                                        children: \"Gemini 2.5 Pro\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 184,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"border border-gray-300 px-4 py-3 text-center font-semibold\",\n                                                                        children: \"Grok 4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 185,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 180,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                            lineNumber: 179,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 px-4 py-3 font-medium\",\n                                                                            children: \"MMLU Pro (Advanced Knowledge)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 190,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 px-4 py-3 text-center\",\n                                                                            children: \"84.2%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 191,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 px-4 py-3 text-center\",\n                                                                            children: \"86.1%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 192,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 px-4 py-3 text-center bg-green-50\",\n                                                                            children: \"87.3%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 193,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 px-4 py-3 text-center\",\n                                                                            children: \"85.3%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 194,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                    lineNumber: 189,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 px-4 py-3 font-medium\",\n                                                                            children: \"HumanEval (Coding)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 197,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 px-4 py-3 text-center\",\n                                                                            children: \"89.7%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 198,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 px-4 py-3 text-center bg-green-50\",\n                                                                            children: \"92.3%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 199,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 px-4 py-3 text-center\",\n                                                                            children: \"87.4%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 200,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 px-4 py-3 text-center\",\n                                                                            children: \"88.1%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 201,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                    lineNumber: 196,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 px-4 py-3 font-medium\",\n                                                                            children: \"GPQA Diamond (Scientific Reasoning)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 204,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 px-4 py-3 text-center\",\n                                                                            children: \"85.5%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 205,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 px-4 py-3 text-center\",\n                                                                            children: \"84.9%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 206,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 px-4 py-3 text-center bg-green-50\",\n                                                                            children: \"86.4%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 207,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 px-4 py-3 text-center\",\n                                                                            children: \"83.7%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 208,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                    lineNumber: 203,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 px-4 py-3 font-medium\",\n                                                                            children: \"SWE-bench Verified (Real-world Coding)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 211,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 px-4 py-3 text-center\",\n                                                                            children: \"87.2%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 212,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 px-4 py-3 text-center bg-green-50\",\n                                                                            children: \"89.7%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 213,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 px-4 py-3 text-center\",\n                                                                            children: \"85.1%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 214,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 px-4 py-3 text-center\",\n                                                                            children: \"84.3%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 215,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                    lineNumber: 210,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 px-4 py-3 font-medium\",\n                                                                            children: \"MMMU (Multimodal Understanding)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 218,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 px-4 py-3 text-center\",\n                                                                            children: \"74.8%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 219,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 px-4 py-3 text-center\",\n                                                                            children: \"75.2%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 220,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 px-4 py-3 text-center bg-green-50\",\n                                                                            children: \"78.9%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 221,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"border border-gray-300 px-4 py-3 text-center\",\n                                                                            children: \"76.5%\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 222,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                    lineNumber: 217,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                            lineNumber: 188,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-gray-900 mt-12 mb-6\",\n                                                children: \"Task-Specific Recommendations: Which AI Model to Choose\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-2xl font-semibold text-blue-900 mb-4\",\n                                                                children: \"\\uD83D\\uDCBB Software Development & Coding\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 232,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-blue-800 mb-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Winner: Claude 4 Opus\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                    lineNumber: 233,\n                                                                    columnNumber: 55\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 233,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-blue-700\",\n                                                                children: \"Claude 4 Opus dominates coding benchmarks with 92.3% on HumanEval and 89.7% on SWE-bench Verified. It excels at complex code generation, architectural design, debugging, and explaining intricate algorithms. Best for: Python, JavaScript, Rust, React, API development, and comprehensive code reviews.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 234,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-4 p-4 bg-blue-100 rounded\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-blue-800\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                            children: \"Pro Tip:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 238,\n                                                                            columnNumber: 60\n                                                                        }, this),\n                                                                        \" Use Claude 4 Opus for complex coding tasks and architectural decisions, then GPT o3 for rapid prototyping and iteration.\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                    lineNumber: 238,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 237,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-200 rounded-lg p-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-2xl font-semibold text-purple-900 mb-4\",\n                                                                children: \"✍️ Content Writing & Creative Tasks\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 243,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-purple-800 mb-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Winner: Claude 4 Opus\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                    lineNumber: 244,\n                                                                    columnNumber: 57\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 244,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-purple-700\",\n                                                                children: \"Claude 4 Opus produces the most nuanced, well-structured content with superior reasoning and creativity. Its 500K context window allows for maintaining consistency across very long documents and complex narratives. Best for: Blog posts, technical documentation, creative writing, storytelling, and in-depth analysis.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 245,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-4 p-4 bg-purple-100 rounded\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-purple-800\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                            children: \"Pro Tip:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 249,\n                                                                            columnNumber: 62\n                                                                        }, this),\n                                                                        \" Claude 4 Opus excels at maintaining brand voice and tone across multiple pieces of content while handling complex creative requirements.\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                    lineNumber: 249,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 248,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg p-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-2xl font-semibold text-green-900 mb-4\",\n                                                                children: \"\\uD83D\\uDCCA Data Analysis & Research\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 254,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-green-800 mb-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Winner: Gemini 2.5 Pro\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                    lineNumber: 255,\n                                                                    columnNumber: 56\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 255,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-green-700\",\n                                                                children: \"With its massive 2M context window, Google Search integration, and 86.4% GPQA Diamond score, Gemini 2.5 Pro excels at processing enormous datasets and providing real-time insights. Advanced multimodal capabilities handle charts, graphs, and visual data seamlessly. Best for: Market research, data visualization, trend analysis, scientific research, and complex mathematical reasoning.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 256,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-4 p-4 bg-green-100 rounded\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-green-800\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                            children: \"Pro Tip:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 260,\n                                                                            columnNumber: 61\n                                                                        }, this),\n                                                                        \" Combine Gemini 2.5 Pro's analytical power with Grok 4's real-time data access for comprehensive market intelligence.\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                    lineNumber: 260,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 259,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                        lineNumber: 253,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-r from-orange-50 to-red-50 border border-orange-200 rounded-lg p-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-2xl font-semibold text-orange-900 mb-4\",\n                                                                children: \"\\uD83D\\uDDE3️ Conversational AI & Social Media\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 265,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-orange-800 mb-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Winner: Grok 4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                    lineNumber: 266,\n                                                                    columnNumber: 57\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 266,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-orange-700\",\n                                                                children: \"Grok 4's real-time access to X (Twitter) data, function calling capabilities, and conversational personality make it ideal for social media management and trend analysis. With structured outputs and reasoning capabilities, it's perfect for dynamic content creation. Best for: Social media content, trend monitoring, conversational chatbots, and real-time insights.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 267,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-4 p-4 bg-orange-100 rounded\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-orange-800\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                            children: \"Pro Tip:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                            lineNumber: 271,\n                                                                            columnNumber: 62\n                                                                        }, this),\n                                                                        \" Grok 4's less restrictive nature and real-time data access make it valuable for creative brainstorming and unconventional problem-solving.\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                    lineNumber: 271,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 270,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-gray-900 mt-12 mb-6\",\n                                                children: \"Context Window Comparison: How Much Data Can Each Model Handle?\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 border border-gray-200 rounded-lg p-6 my-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center p-4 bg-white rounded-lg border\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold text-gray-900 mb-2\",\n                                                                        children: \"Gemini 2.5 Pro\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 281,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-3xl font-bold text-blue-600 mb-1\",\n                                                                        children: \"2M\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 282,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: \"tokens\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 283,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500 mt-2\",\n                                                                        children: \"~1,500 pages\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 284,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 280,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center p-4 bg-white rounded-lg border\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold text-gray-900 mb-2\",\n                                                                        children: \"Claude 4 Opus\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 287,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-3xl font-bold text-purple-600 mb-1\",\n                                                                        children: \"500K\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 288,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: \"tokens\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 289,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500 mt-2\",\n                                                                        children: \"~375 pages\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 290,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 286,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center p-4 bg-white rounded-lg border\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold text-gray-900 mb-2\",\n                                                                        children: \"Grok 4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 293,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-3xl font-bold text-orange-600 mb-1\",\n                                                                        children: \"256K\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 294,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: \"tokens\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 295,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500 mt-2\",\n                                                                        children: \"~192 pages\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 296,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 292,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center p-4 bg-white rounded-lg border\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold text-gray-900 mb-2\",\n                                                                        children: \"GPT o3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 299,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-3xl font-bold text-green-600 mb-1\",\n                                                                        children: \"200K\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 300,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600\",\n                                                                        children: \"tokens\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 301,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500 mt-2\",\n                                                                        children: \"~150 pages\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 302,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 298,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-4 p-4 bg-blue-50 rounded\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-blue-800\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Why Context Window Matters:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                    lineNumber: 306,\n                                                                    columnNumber: 58\n                                                                }, this),\n                                                                \" Larger context windows allow you to process longer documents, maintain conversation history, and provide more comprehensive analysis without losing important details.\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                            lineNumber: 306,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                        lineNumber: 305,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-gray-900 mt-12 mb-6\",\n                                                children: \"Cost Analysis: Getting the Best Value\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-6 my-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-yellow-900 mb-4\",\n                                                        children: \"\\uD83D\\uDCB0 Pricing Breakdown (per 1M tokens)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                        lineNumber: 313,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold text-yellow-800 mb-2\",\n                                                                        children: \"Input Tokens:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 316,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                        className: \"text-yellow-700 space-y-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"• Gemini 2.5 Pro: $1.25-$2.50 (cheapest)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                                lineNumber: 318,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"• GPT o3: $2.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                                lineNumber: 319,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"• Grok 4: $3.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                                lineNumber: 320,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"• Claude 4 Opus: $15.00 (most expensive)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                                lineNumber: 321,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 317,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 315,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold text-yellow-800 mb-2\",\n                                                                        children: \"Output Tokens:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 325,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                        className: \"text-yellow-700 space-y-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"• GPT o3: $8.00 (cheapest)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                                lineNumber: 327,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"• Gemini 2.5 Pro: $10.00-$15.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                                lineNumber: 328,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"• Grok 4: $15.00\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                                lineNumber: 329,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"• Claude 4 Opus: $75.00 (most expensive)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                                lineNumber: 330,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 326,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 324,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-4 p-4 bg-yellow-100 rounded\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-yellow-800\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Note:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                    lineNumber: 335,\n                                                                    columnNumber: 60\n                                                                }, this),\n                                                                \" Gemini 2.5 Pro pricing varies by prompt size (≤200k vs >200k tokens). All prices are per 1 million tokens.\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                            lineNumber: 335,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-gray-900 mt-12 mb-6\",\n                                                children: \"The Smart Solution: Multi-Model AI Routing\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Instead of choosing just one AI model, the smartest approach in 2025 is to use the right model for each specific task. This is where AI routing platforms like RouKey become invaluable.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-r from-[#ff6b35]/10 to-[#f7931e]/10 border border-[#ff6b35]/30 rounded-lg p-8 my-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-2xl font-semibold text-gray-900 mb-4\",\n                                                        children: \"\\uD83D\\uDE80 Why Multi-Model Routing is the Future\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"space-y-3 text-gray-700\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                className: \"flex items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-[#ff6b35] mr-3 mt-1\",\n                                                                        children: \"•\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 349,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                children: \"Cost Optimization:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                                lineNumber: 350,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            \" Use cheaper models for simple tasks, premium models for complex ones\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 350,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 348,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                className: \"flex items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-[#ff6b35] mr-3 mt-1\",\n                                                                        children: \"•\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 353,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                children: \"Performance Maximization:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                                lineNumber: 354,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            \" Route each task to the model that performs best for that specific use case\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 354,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 352,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                className: \"flex items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-[#ff6b35] mr-3 mt-1\",\n                                                                        children: \"•\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 357,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                children: \"Reliability:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                                lineNumber: 358,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            \" Automatic fallbacks ensure your application never goes down\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 358,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 356,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                className: \"flex items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-[#ff6b35] mr-3 mt-1\",\n                                                                        children: \"•\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 361,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                children: \"Future-Proof:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                                lineNumber: 362,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            \" Easily add new models as they become available\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                        lineNumber: 362,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 360,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                        lineNumber: 347,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                lineNumber: 345,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-gray-900 mt-12 mb-6\",\n                                                children: \"Conclusion: The Multi-Model Future\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                lineNumber: 367,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"The AI model wars of 2025 have produced revolutionary breakthroughs, but the real winner is the user who can leverage all these models strategically. Claude 4 Opus for complex coding and reasoning, GPT o3 for rapid iteration and creative tasks, Gemini 2.5 Pro for data analysis and research, and Grok 4 for real-time insights and social intelligence – each has its place in a well-architected AI system.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                lineNumber: 369,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"The future belongs to intelligent routing systems that can automatically select the best model for each task, optimize costs, and provide seamless fallbacks. This isn't just about having access to multiple models – it's about using them intelligently to maximize performance while minimizing costs.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                lineNumber: 373,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-r from-[#ff6b35] to-[#f7931e] rounded-2xl p-8 text-white my-12\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-2xl font-bold mb-4\",\n                                                        children: \"Ready to Use All AI Models Intelligently?\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                        lineNumber: 379,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg mb-6 opacity-90\",\n                                                        children: \"Stop choosing between AI models. RouKey's intelligent routing lets you use GPT o3, Claude 4 Opus, Gemini 2.5 Pro, and Grok 4 seamlessly with your own API keys. Save up to 70% on AI costs while maximizing performance with the latest 2025 models.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                        lineNumber: 380,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col sm:flex-row gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                                href: \"/\",\n                                                                className: \"inline-flex items-center justify-center px-6 py-3 bg-white text-[#ff6b35] font-semibold rounded-lg hover:bg-gray-100 transition-colors\",\n                                                                children: \"Start Free with RouKey\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 384,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                                href: \"/pricing\",\n                                                                className: \"inline-flex items-center justify-center px-6 py-3 border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-[#ff6b35] transition-colors\",\n                                                                children: \"View Pricing\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                                lineNumber: 390,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                                lineNumber: 378,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_Footer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n                lineNumber: 404,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\blog\\\\openai-vs-claude-vs-gemini-2025\\\\page.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n_c = OpenAIVsClaudeVsGemini;\nvar _c;\n$RefreshReg$(_c, \"OpenAIVsClaudeVsGemini\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/blog/openai-vs-claude-vs-gemini-2025/page.tsx\n"));

/***/ })

});