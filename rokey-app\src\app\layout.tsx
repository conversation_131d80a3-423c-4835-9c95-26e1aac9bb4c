import type { Metadata } from "next";
import { Inter } from "next/font/google";
import { Suspense } from "react";
import "./globals.css";
import "../styles/design-system.css";
import "../styles/mobile-responsive.css";
import "../styles/mobile-first.css";
import ConditionalLayout from "@/components/ConditionalLayout";
import DocumentTitleUpdater from "@/components/DocumentTitleUpdater";
import PerformanceTracker from "@/components/PerformanceTracker";
import GlobalPrefetcher from "@/components/GlobalPrefetcher";
import Script from "next/script";
import { SpeedInsights } from '@vercel/speed-insights/next';

const inter = Inter({
  subsets: ["latin"],
  display: 'swap',
  variable: '--font-inter'
});

export const metadata: Metadata = {
  metadataBase: new URL(process.env.NEXT_PUBLIC_SITE_URL || 'https://roukey.online'),
  title: "RouKey - Smart AI Gateway with Your Own Keys",
  description: "Connect to 50+ AI providers using your own API keys. Smart routing, fallback protection, and cost optimization. One unified interface for OpenAI, Claude, Gemini, and more.",
  keywords: ["AI gateway", "API routing", "OpenAI", "Claude", "Gemini", "AI integration", "BYOK", "smart routing"],
  authors: [{ name: "Okoro David Chukwunyerem" }],
  creator: "Okoro David Chukwunyerem",
  publisher: "RouKey",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://roukey.online',
    siteName: 'RouKey',
    title: 'RouKey - Smart AI Gateway with Your Own Keys',
    description: 'Connect to 50+ AI providers using your own API keys. Smart routing, fallback protection, and cost optimization.',
    images: [
      {
        url: '/RouKey_Logo_GLOW.png',
        width: 1200,
        height: 630,
        alt: 'RouKey - Smart AI Gateway',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'RouKey - Smart AI Gateway with Your Own Keys',
    description: 'Connect to 50+ AI providers using your own API keys. Smart routing, fallback protection, and cost optimization.',
    images: ['/RouKey_Logo_GLOW.png'],
    creator: '@roukey_ai',
  },
  icons: {
    icon: [
      {
        url: '/RouKey_Logo_GLOW.png',
        sizes: '32x32',
        type: 'image/png',
      },
      {
        url: '/RouKey_Logo_GLOW.png',
        sizes: '16x16',
        type: 'image/png',
      }
    ],
    apple: [
      {
        url: '/RouKey_Logo_GLOW.png',
        sizes: '180x180',
        type: 'image/png',
      }
    ],
    shortcut: '/RouKey_Logo_GLOW.png',
  },
};

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={inter.variable}>
      <head>
        {/* Favicon and icons */}
        <link rel="icon" href="/RouKey_Logo_GLOW.png" type="image/png" sizes="32x32" />
        <link rel="icon" href="/RouKey_Logo_GLOW.png" type="image/png" sizes="16x16" />
        <link rel="apple-touch-icon" href="/RouKey_Logo_GLOW.png" sizes="180x180" />
        <link rel="manifest" href="/manifest.json" />

        {/* Preload critical resources */}
        <link rel="preload" href="/api/custom-configs" as="fetch" crossOrigin="anonymous" />
        <link rel="preload" href="/api/system-status" as="fetch" crossOrigin="anonymous" />

        {/* DNS prefetch for external resources */}
        <link rel="dns-prefetch" href="//fonts.googleapis.com" />

        {/* Preconnect to critical origins */}
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="" />

        {/* Resource hints for navigation */}

        {/* Structured Data for Google Search */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "Organization",
              "name": "RouKey",
              "url": "https://roukey.online",
              "logo": "https://roukey.online/RouKey_Logo_GLOW.png",
              "description": "Smart AI routing platform that connects 50+ AI providers using your own API keys. Intelligent routing, fallback protection, and cost optimization.",
              "foundingDate": "2025",
              "founder": {
                "@type": "Person",
                "name": "Okoro David Chukwunyerem"
              },
              "sameAs": [
                "https://www.producthunt.com/products/roukey"
              ],
              "contactPoint": {
                "@type": "ContactPoint",
                "email": "<EMAIL>",
                "contactType": "customer service"
              }
            })
          }}
        />
        <link rel="prefetch" href="/dashboard" />
        <link rel="prefetch" href="/playground" />
        <link rel="prefetch" href="/logs" />
        <link rel="prefetch" href="/my-models" />
      </head>
      <body className="font-sans antialiased bg-[#1B1C1D]">
        <Suspense fallback={null}>
          <DocumentTitleUpdater />
        </Suspense>
        <GlobalPrefetcher />
        <PerformanceTracker
          enableUserBehaviorTracking={true}
          enableNavigationTracking={true}
          enableInteractionTracking={true}
        />
        <ConditionalLayout>
          {children}
        </ConditionalLayout>

        {/* Vercel Speed Insights */}
        <SpeedInsights />

        {/* Performance monitoring in development - temporarily disabled due to Supabase conflicts */}
        {process.env.NODE_ENV === 'development' && process.env.ENABLE_PERFORMANCE_MONITOR === 'true' && (
          <Script
            src="/performance-monitor.js"
            strategy="afterInteractive"
          />
        )}

        {/* Service Worker registration and cache preloading */}
        <Script id="sw-register" strategy="afterInteractive">
          {`
            if ('serviceWorker' in navigator) {
              window.addEventListener('load', function() {
                navigator.serviceWorker.register('/sw.js')
                  .then(function(registration) {
                    console.log('✅ Service Worker registered successfully');

                    // Preload critical data after SW is ready
                    if (window.location.pathname === '/') {
                      // Preload landing page data
                      fetch('/api/system-status').catch(() => {});

                      // Prefetch all critical pages immediately
                      setTimeout(() => {
                        const criticalPages = ['/features', '/pricing', '/about', '/auth/signin', '/auth/signup'];
                        criticalPages.forEach(page => {
                          const link = document.createElement('link');
                          link.rel = 'prefetch';
                          link.href = page;
                          document.head.appendChild(link);
                        });
                      }, 500); // Much faster prefetching
                    }
                  })
                  .catch(function(registrationError) {
                    console.warn('⚠️ Service Worker registration failed:', registrationError);
                  });
              });
            }
          `}
        </Script>
      </body>
    </html>
  );
}
